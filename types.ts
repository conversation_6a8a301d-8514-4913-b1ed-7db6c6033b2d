export interface CalculatorInput {
  length: string;
  lengthUnit: 'm' | 'ft' | 'km';
  width: string;
  widthUnit: 'm' | 'ft';
  thickness: string;
  thicknessUnit: 'mm' | 'cm' | 'in';
  bitumenContent: string;
  mixDensity: string;
  mixDensityUnit: 'kg/m³' | 'lb/ft³';
  pricePerTonne?: string;
}

export interface CalculationResult {
  volume: number;
  totalWeightKg: number;
  bitumenWeightKg: number;
  aggregateWeightKg: number;
  totalWeightTonnes: number;
  bitumenWeightTonnes: number;
  aggregateWeightTonnes: number;
  totalCost?: number;
}
