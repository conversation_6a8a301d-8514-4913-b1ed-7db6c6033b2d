import React from 'react';
import { CalculatorInput } from '../types';
import InputField from './InputField';

interface CalculatorFormProps {
  inputs: CalculatorInput;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  onCalculate: () => void;
  onReset: () => void;
}

const lengthUnits = [{value: 'm', label: 'm'}, {value: 'ft', label: 'ft'}, {value: 'km', label: 'km'}];
const widthUnits = [{value: 'm', label: 'm'}, {value: 'ft', label: 'ft'}];
const thicknessUnits = [{value: 'mm', label: 'mm'}, {value: 'cm', label: 'cm'}, {value: 'in', label: 'in'}];
const densityUnits = [{value: 'kg/m³', label: 'kg/m³'}, {value: 'lb/ft³', label: 'lb/ft³'}];

const CalculatorForm: React.FC<CalculatorFormProps> = ({ inputs, onInputChange, onCalculate, onReset }) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onCalculate();
  };
  
  return (
    <div className="bg-slate-800/50 p-6 rounded-2xl shadow-lg border border-slate-700/50">
      <h2 className="text-2xl font-semibold text-slate-100 mb-6">Project Parameters</h2>
      <form onSubmit={handleSubmit} className="space-y-5">
        <fieldset className="space-y-4">
          <legend className="text-sm font-medium text-cyan-400 mt-20">Dimensions</legend>
          <InputField 
            label="Length" 
            name="length" 
            value={inputs.length} 
            onChange={onInputChange} 
            unit={inputs.lengthUnit}
            unitName="lengthUnit"
            unitOptions={lengthUnits}
            placeholder="e.g., 1000"
          />
          <InputField 
            label="Width" 
            name="width" 
            value={inputs.width} 
            onChange={onInputChange} 
            unit={inputs.widthUnit}
            unitName="widthUnit"
            unitOptions={widthUnits}
            placeholder="e.g., 3.5"
          />
          <InputField 
            label="Thickness" 
            name="thickness" 
            value={inputs.thickness} 
            onChange={onInputChange} 
            unit={inputs.thicknessUnit}
            unitName="thicknessUnit"
            unitOptions={thicknessUnits}
            placeholder="e.g., 50"
          />
        </fieldset>
        
        <fieldset className="space-y-4 pt-4">
          <legend className="text-sm font-medium text-cyan-400 mb-2">Mix Properties</legend>
          <InputField 
            label="Bitumen Content" 
            name="bitumenContent" 
            value={inputs.bitumenContent} 
            onChange={onInputChange} 
            unit="%"
            placeholder="e.g., 5.5"
          />
          <InputField 
            label="Mix Density" 
            name="mixDensity" 
            value={inputs.mixDensity} 
            onChange={onInputChange} 
            unit={inputs.mixDensityUnit}
            unitName="mixDensityUnit"
            unitOptions={densityUnits}
            placeholder="e.g., 2350"
          />
        </fieldset>

        <fieldset className="space-y-4 pt-4">
          <legend className="text-sm font-medium text-slate-400 mb-2">Cost Estimation (Optional)</legend>
          <InputField 
            label="Price per Tonne" 
            name="pricePerTonne" 
            value={inputs.pricePerTonne ?? ''} 
            onChange={onInputChange} 
            unit="$ / tonne"
            placeholder="e.g., 500"
            isRequired={false}
          />
        </fieldset>
        
        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <button type="submit" className="flex-1 w-full bg-cyan-600 hover:bg-cyan-500 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-md">
            Calculate
          </button>
          <button type="button" onClick={onReset} className="flex-1 w-full bg-slate-600 hover:bg-slate-500 text-slate-100 font-bold py-3 px-4 rounded-lg transition-all duration-300">
            Reset
          </button>
        </div>
      </form>
    </div>
  );
};

export default CalculatorForm;
