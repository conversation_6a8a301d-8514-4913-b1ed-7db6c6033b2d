import React from 'react';

interface InputFieldProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  unit: string;
  unitName?: string;
  unitOptions?: { value: string; label: string }[];
  placeholder?: string;
  isRequired?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({ 
  label, 
  name, 
  value, 
  onChange, 
  unit, 
  unitName,
  unitOptions,
  placeholder, 
  isRequired = true 
}) => {
  const commonInputStyles = "bg-slate-700/50 border border-slate-600 text-slate-100 p-3 transition-all duration-200 outline-none";
  const focusStyles = "focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 focus:z-10";

  return (
    <div>
      <label htmlFor={name} className="block text-sm font-medium text-slate-300 mb-1">
        {label}
      </label>
      <div className="relative flex items-stretch">
        <input
          type="number"
          id={name}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={`${commonInputStyles} ${focusStyles} w-full rounded-lg ${unitOptions ? 'rounded-r-none' : ''}`}
          required={isRequired}
          min="0"
          step="any"
        />
        {unitOptions && unitName ? (
            <select
                id={unitName}
                name={unitName}
                value={unit}
                onChange={onChange}
                className={`${commonInputStyles} ${focusStyles} rounded-l-none rounded-r-lg border-l-0 appearance-none bg-no-repeat bg-right pr-8`}
                style={{
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: 'right 0.5rem center',
                    backgroundSize: '1.5em 1.5em',
                }}

            >
            {unitOptions.map(opt => (
                <option key={opt.value} value={opt.value} className="bg-slate-800">
                {opt.label}
                </option>
            ))}
            </select>
        ) : (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <span className="text-slate-400 text-sm">{unit}</span>
            </div>
        )}
      </div>
    </div>
  );
};

export default InputField;
