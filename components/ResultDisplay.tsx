import React from 'react';
import { CalculationResult } from '../types';
import { RoadIcon } from './icons/RoadIcon';

interface ResultDisplayProps {
  result: CalculationResult | null;
}

const ResultCard: React.FC<{ title: string; valueKg: string; valueTonnes: string; color: string }> = ({ title, valueKg, valueTonnes, color }) => (
  <div className={`bg-slate-800 p-4 rounded-lg border-l-4 ${color}`}>
    <h3 className="text-sm font-medium text-slate-400">{title}</h3>
    <p className="text-2xl font-bold text-slate-100">{valueTonnes} <span className="text-base font-normal text-slate-300">Tonnes</span></p>
    <p className="text-sm text-slate-400">({valueKg} kg)</p>
  </div>
);

const ResultDisplay: React.FC<ResultDisplayProps> = ({ result }) => {
  if (!result) {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-slate-800/50 p-6 rounded-2xl shadow-lg border border-slate-700/50 text-center">
        <RoadIcon className="w-20 h-20 text-slate-600 mb-4" />
        <h2 className="text-2xl font-semibold text-slate-300">Results Panel</h2>
        <p className="text-slate-400 mt-2 max-w-sm">
          Your calculated material quantities will be displayed here once you enter the project parameters and click "Calculate".
        </p>
      </div>
    );
  }

  return (
    <div className="bg-slate-800/50 p-6 rounded-2xl shadow-lg border border-slate-700/50 h-full">
      <h2 className="text-2xl font-semibold text-slate-100 mb-6">Calculation Results</h2>
      <div className="space-y-4">
        <div className="bg-slate-900/50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-slate-400">Total Mix Volume</h3>
          <p className="text-3xl font-bold text-cyan-400">{result.volume.toLocaleString(undefined, { maximumFractionDigits: 2 })} <span className="text-xl font-normal text-slate-300">m³</span></p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <ResultCard 
                title="Total Mix Weight" 
                valueKg={result.totalWeightKg.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                valueTonnes={result.totalWeightTonnes.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                color="border-cyan-500"
            />
            <ResultCard 
                title="Weight of Bitumen"
                valueKg={result.bitumenWeightKg.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                valueTonnes={result.bitumenWeightTonnes.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                color="border-amber-500"
            />
        </div>
        <ResultCard 
            title="Weight of Aggregates"
            valueKg={result.aggregateWeightKg.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            valueTonnes={result.aggregateWeightTonnes.toLocaleString(undefined, { maximumFractionDigits: 2 })}
            color="border-slate-500"
        />
        {result.totalCost && result.totalCost > 0 && (
          <div className="bg-green-900/20 p-4 rounded-lg border-l-4 border-green-500 mt-4">
            <h3 className="text-sm font-medium text-green-300">Estimated Total Cost</h3>
            <p className="text-3xl font-bold text-green-400">
              {result.totalCost.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
                maximumFractionDigits: 2,
              })}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultDisplay;
