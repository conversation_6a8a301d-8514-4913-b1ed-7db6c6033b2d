---
import config from '~/config/config.json';

import Header from '../components/Header.astro';
import Calculator from '../components/Calculator';
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bitumen Calculator</title>
    <!-- favicon -->
    <link rel="shortcut icon" href={config.site.favicon} />
    <!-- theme meta -->
    <meta name="theme-name" content="palindromechecker" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#fff" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#000" />
    <meta name="generator" content={Astro.generator} />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <!-- canonical url -->
    {canonical && <link rel="canonical" href={canonical} item-prop="url" />}

    <!-- meta-description -->
    <meta name="description" content={plainify(description ? description : config.metadata.meta_description)} />

    <!-- author from config.json -->
    <meta name="author" content={config.metadata.meta_author} />

    <!-- og-title -->
    <meta property="og:title" content={plainify(meta_title ? meta_title : title ? title : config.site.title)} />

    <!-- og-description -->
    <meta property="og:description" content={plainify(description ? description : config.metadata.meta_description)} />
    <meta property="og:type" content="website" />
    <meta property="og:url" content={`${config.site.base_url}/${Astro.url.pathname.replace('/', '')}`} />

    <!-- twitter-title -->
    <meta name="twitter:title" content={plainify(meta_title ? meta_title : title ? title : config.site.title)} />

    <!-- twitter-description -->
    <meta name="twitter:description" content={plainify(description ? description : config.metadata.meta_description)} />

    <!-- og-image -->
    <meta property="og:image" content={`${config.site.base_url}${image ? image : config.metadata.meta_image}`} />

    <!-- twitter-image -->
    <meta name="twitter:image" content={`${config.site.base_url}${image ? image : config.metadata.meta_image}`} />
    <meta name="twitter:card" content="summary_large_image" />
  </head>
  <body class="bg-slate-900 text-slate-200">
    <div class="min-h-screen bg-slate-900 font-sans p-4 sm:p-6 lg:p-8">
      <div class="max-w-4xl mx-auto">
        <Header />
        <Calculator client:load />
        <footer class="text-center mt-12 text-slate-500 text-sm">
          <p>&copy; {new Date().getFullYear()} Bitumen Calculator. Built by BigTechies.</p>
        </footer>
      </div>
    </div>
  </body>
</html>
