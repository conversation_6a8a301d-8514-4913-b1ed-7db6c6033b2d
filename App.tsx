import React, { useState, useCallback } from 'react';
import { CalculatorInput, CalculationResult } from './types';
import Header from './components/Header';
import CalculatorForm from './components/CalculatorForm';
import ResultDisplay from './components/ResultDisplay';

const initialInputs: CalculatorInput = {
  length: '1000',
  lengthUnit: 'm',
  width: '3.5',
  widthUnit: 'm',
  thickness: '50',
  thicknessUnit: 'mm',
  bitumenContent: '5.5',
  mixDensity: '2350',
  mixDensityUnit: 'kg/m³',
  pricePerTonne: '',
};

const CONVERSION_FACTORS = {
  // Length to meters
  m: 1,
  ft: 0.3048,
  km: 1000,
  // Thickness to meters
  mm: 0.001,
  cm: 0.01,
  in: 0.0254,
  // Density to kg/m³
  'kg/m³': 1,
  'lb/ft³': 16.0185,
};


const App: React.FC = () => {
  const [inputs, setInputs] = useState<CalculatorInput>(initialInputs);
  const [result, setResult] = useState<CalculationResult | null>(null);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setInputs(prev => ({ ...prev, [name]: value }));
  }, []);

  const calculate = useCallback(() => {
    // Convert all inputs to base units (meters and kg/m³) for calculation
    const length = parseFloat(inputs.length) * CONVERSION_FACTORS[inputs.lengthUnit];
    const width = parseFloat(inputs.width) * CONVERSION_FACTORS[inputs.widthUnit];
    const thickness = parseFloat(inputs.thickness) * CONVERSION_FACTORS[inputs.thicknessUnit];
    const bitumenContent = parseFloat(inputs.bitumenContent);
    const mixDensity = parseFloat(inputs.mixDensity) * CONVERSION_FACTORS[inputs.mixDensityUnit];
    const pricePerTonne = inputs.pricePerTonne ? parseFloat(inputs.pricePerTonne) : 0;

    if (isNaN(length) || isNaN(width) || isNaN(thickness) || isNaN(bitumenContent) || isNaN(mixDensity)) {
      setResult(null);
      return;
    }
    
    if (length <= 0 || width <= 0 || thickness <= 0 || bitumenContent <= 0 || mixDensity <= 0) {
        setResult(null);
        return;
    }

    const volume = length * width * thickness;
    const totalWeightKg = volume * mixDensity;
    const bitumenWeightKg = totalWeightKg * (bitumenContent / 100);
    const aggregateWeightKg = totalWeightKg - bitumenWeightKg;
    
    const totalWeightTonnes = totalWeightKg / 1000;
    const bitumenWeightTonnes = bitumenWeightKg / 1000;
    const aggregateWeightTonnes = aggregateWeightKg / 1000;

    const totalCost = pricePerTonne > 0 ? totalWeightTonnes * pricePerTonne : undefined;

    setResult({
      volume,
      totalWeightKg,
      bitumenWeightKg,
      aggregateWeightKg,
      totalWeightTonnes,
      bitumenWeightTonnes,
      aggregateWeightTonnes,
      totalCost,
    });
  }, [inputs]);

  const resetCalculator = useCallback(() => {
    setInputs(initialInputs);
    setResult(null);
  }, []);

  return (
    <div className="min-h-screen bg-slate-900 font-sans p-4 sm:p-6 lg:p-8">
      <div className="max-w-4xl mx-auto">
        <Header />
        <main className="mt-8 grid grid-cols-1 md:grid-cols-5 gap-8">
          <div className="md:col-span-2">
            <CalculatorForm 
              inputs={inputs} 
              onInputChange={handleInputChange}
              onCalculate={calculate}
              onReset={resetCalculator}
            />
          </div>
          <div className="md:col-span-3">
            <ResultDisplay result={result} />
          </div>
        </main>
        <footer className="text-center mt-12 text-slate-500 text-sm">
          <p>&copy; {new Date().getFullYear()} Bitumen Calculator. Built by BigTechies.</p>
        </footer>
      </div>
    </div>
  );
};

export default App;
